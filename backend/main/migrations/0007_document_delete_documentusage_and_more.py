# Generated by Django 5.2.4 on 2025-07-30 02:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0006_auto_20250723_1613'),
    ]

    operations = [
        migrations.CreateModel(
            name='Document',
            fields=[
                ('document_id', models.CharField(help_text='文档内容的SHA256哈希值，作为唯一标识符', max_length=64, primary_key=True, serialize=False, verbose_name='文档ID (SHA256)')),
                ('title', models.TextField(help_text='文档标题', verbose_name='标题')),
                ('abstract', models.TextField(blank=True, help_text='文档摘要或概述', verbose_name='摘要')),
                ('full_text', models.TextField(help_text='完整的文档内容', verbose_name='全文内容')),
                ('authors', models.JSONField(blank=True, default=list, help_text="作者信息列表，格式：[{'name': '作者名', 'affiliation': '机构', 'email': '邮箱'}]", verbose_name='作者列表')),
                ('publication_date', models.DateField(blank=True, null=True, verbose_name='发表日期')),
                ('journal', models.CharField(blank=True, max_length=500, verbose_name='期刊/会议')),
                ('volume', models.CharField(blank=True, max_length=50, verbose_name='卷号')),
                ('issue', models.CharField(blank=True, max_length=50, verbose_name='期号')),
                ('pages', models.CharField(blank=True, max_length=50, verbose_name='页码')),
                ('doi', models.CharField(blank=True, max_length=200, null=True, unique=True, verbose_name='DOI')),
                ('keywords', models.JSONField(blank=True, default=list, help_text='关键词列表', verbose_name='关键词')),
                ('research_field', models.CharField(blank=True, max_length=200, verbose_name='研究领域')),
                ('document_type', models.CharField(choices=[('article', '期刊论文'), ('conference', '会议论文'), ('book', '书籍'), ('chapter', '书籍章节'), ('thesis', '学位论文'), ('report', '研究报告'), ('other', '其他')], default='article', max_length=50, verbose_name='文档类型')),
                ('language', models.CharField(choices=[('zh', '中文'), ('en', '英文'), ('other', '其他')], default='zh', max_length=10, verbose_name='语言')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('uploaded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_documents', to=settings.AUTH_USER_MODEL, verbose_name='上传用户')),
            ],
            options={
                'verbose_name': '文档',
                'verbose_name_plural': '文档',
            },
        ),
        migrations.DeleteModel(
            name='DocumentUsage',
        ),
        migrations.AddIndex(
            model_name='document',
            index=models.Index(fields=['created_at'], name='main_docume_created_6202b9_idx'),
        ),
        migrations.AddIndex(
            model_name='document',
            index=models.Index(fields=['uploaded_by', 'created_at'], name='main_docume_uploade_4230f6_idx'),
        ),
        migrations.AddIndex(
            model_name='document',
            index=models.Index(fields=['research_field'], name='main_docume_researc_2a3ca6_idx'),
        ),
        migrations.AddIndex(
            model_name='document',
            index=models.Index(fields=['document_type'], name='main_docume_documen_fc9936_idx'),
        ),
    ]
