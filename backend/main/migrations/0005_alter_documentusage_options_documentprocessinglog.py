# Generated by Django 5.2.4 on 2025-07-23 08:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0004_documentusage'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='documentusage',
            options={'verbose_name': '文档使用记录 (已弃用)', 'verbose_name_plural': '文档使用记录 (已弃用)'},
        ),
        migrations.CreateModel(
            name='DocumentProcessingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_id', models.CharField(db_index=True, help_text='文档内容的SHA256哈希值，用于去重', max_length=64, verbose_name='文档ID (SHA256)')),
                ('endpoint', models.CharField(choices=[('summarize', '文档摘要'), ('literature', '文献分析')], max_length=20, verbose_name='处理端点')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='处理时间')),
                ('content_length', models.IntegerField(blank=True, null=True, verbose_name='内容长度')),
                ('processing_time_ms', models.IntegerField(blank=True, null=True, verbose_name='处理时间(毫秒)')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_processing_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '文档处理日志',
                'verbose_name_plural': '文档处理日志',
                'indexes': [models.Index(fields=['user', 'created_at'], name='main_docume_user_id_32d9f6_idx'), models.Index(fields=['user', 'document_id'], name='main_docume_user_id_83e71f_idx'), models.Index(fields=['user', 'created_at', 'document_id'], name='main_docume_user_id_e4d853_idx')],
            },
        ),
    ]
