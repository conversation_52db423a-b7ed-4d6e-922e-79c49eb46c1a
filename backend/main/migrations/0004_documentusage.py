# Generated by Django 5.2.4 on 2025-07-22 08:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0003_alter_user_managers'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(verbose_name='年份')),
                ('month', models.IntegerField(verbose_name='月份')),
                ('document_count', models.IntegerField(default=0, verbose_name='文档数量')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_usage', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '文档使用记录',
                'verbose_name_plural': '文档使用记录',
                'indexes': [models.Index(fields=['user', 'year', 'month'], name='main_docume_user_id_079744_idx')],
                'unique_together': {('user', 'year', 'month')},
            },
        ),
    ]
