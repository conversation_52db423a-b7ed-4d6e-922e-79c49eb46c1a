# Generated by Django 5.2 on 2025-05-29 14:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("main", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="subscription",
            name="subscription_type",
            field=models.CharField(
                choices=[
                    ("free", "免费用户"),
                    ("premium", "高级用户"),
                    ("plus", "专业用户"),
                ],
                default="free",
                max_length=10,
                verbose_name="订阅类型",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="email",
            field=models.EmailField(
                blank=True,
                max_length=254,
                null=True,
                unique=True,
                verbose_name="邮箱地址",
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone",
            field=models.CharField(
                blank=True,
                max_length=11,
                null=True,
                unique=True,
                verbose_name="手机号码",
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="user",
            name="username",
            field=models.Char<PERSON>ield(
                blank=True,
                default="",
                max_length=150,
                unique=True,
                verbose_name="用户名",
            ),
        ),
    ]
