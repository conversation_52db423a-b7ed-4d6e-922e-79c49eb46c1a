from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


# 用户相关模式
class UserSchema(BaseModel):
    id: int
    phone: Optional[str] = None
    email: Optional[str] = None
    username: str
    date_joined: datetime

    class Config:
        from_attributes = True


class UserProfileSchema(BaseModel):
    id: int
    phone: Optional[str] = None
    email: Optional[str] = None
    username: str
    date_joined: datetime
    subscription: 'SubscriptionSchema'

    class Config:
        from_attributes = True


# 认证相关模式
class PhoneAuthSchema(BaseModel):
    phone: str = Field(..., min_length=11, max_length=11)
    verification_code: str = Field(..., min_length=6, max_length=6)


class SendCodeSchema(BaseModel):
    phone: str = Field(..., min_length=11, max_length=11)


class TokenResponseSchema(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserSchema


class RefreshTokenSchema(BaseModel):
    refresh_token: str


class CheckUserExistsSchema(BaseModel):
    phone: str = Field(..., min_length=11, max_length=11)


class UserExistsResponseSchema(BaseModel):
    exists: bool


# 订阅相关模式
class SubscriptionSchema(BaseModel):
    subscription_type: str
    start_date: datetime
    end_date: Optional[datetime] = None
    is_active: bool
    is_premium: bool
    days_remaining: int
    monthly_document_limit: int

    class Config:
        from_attributes = True


# 定价方案模式
class PricingPlanSchema(BaseModel):
    name: str
    title: str
    price: str
    period: str
    features: List[str]
    button_text: str
    button_class: str
    is_current: bool


# 文档相关模式
class AuthorSchema(BaseModel):
    """作者信息模式"""
    name: str
    affiliation: Optional[str] = None
    email: Optional[str] = None


class DocumentUploadRequest(BaseModel):
    """文档上传请求模式"""
    document_id: str = Field(..., min_length=64, max_length=64, description="文档的SHA256哈希值")
    title: str = Field(..., min_length=1, description="文档标题")
    abstract: Optional[str] = Field(None, description="文档摘要")
    full_text: str = Field(..., min_length=1, description="完整文档内容")
    authors: Optional[List[AuthorSchema]] = Field(default_factory=list, description="作者列表")
    publication_date: Optional[str] = Field(None, description="发表日期 (YYYY-MM-DD)")
    journal: Optional[str] = Field(None, description="期刊或会议名称")
    volume: Optional[str] = Field(None, description="卷号")
    issue: Optional[str] = Field(None, description="期号")
    pages: Optional[str] = Field(None, description="页码")
    doi: Optional[str] = Field(None, description="DOI")
    keywords: Optional[List[str]] = Field(default_factory=list, description="关键词列表")
    research_field: Optional[str] = Field(None, description="研究领域")
    document_type: Optional[str] = Field("article", description="文档类型")
    language: Optional[str] = Field("zh", description="语言")

    def __init__(self, **data):
        super().__init__(**data)
        # 验证SHA256格式
        if not self._is_valid_sha256(self.document_id):
            raise ValueError("document_id must be a valid SHA256 hash (64 hexadecimal characters)")

    @staticmethod
    def _is_valid_sha256(hash_string: str) -> bool:
        """验证是否为有效的SHA256哈希"""
        import re
        return bool(re.match(r'^[a-fA-F0-9]{64}$', hash_string))


class DocumentValidationRequest(BaseModel):
    """文档验证请求模式"""
    document_id: str = Field(..., min_length=64, max_length=64, description="文档的SHA256哈希值")

    def __init__(self, **data):
        super().__init__(**data)
        # 验证SHA256格式
        if not self._is_valid_sha256(self.document_id):
            raise ValueError("document_id must be a valid SHA256 hash (64 hexadecimal characters)")

    @staticmethod
    def _is_valid_sha256(hash_string: str) -> bool:
        """验证是否为有效的SHA256哈希"""
        import re
        return bool(re.match(r'^[a-fA-F0-9]{64}$', hash_string))


class DocumentResponse(BaseModel):
    """文档响应模式"""
    document_id: str
    title: str
    abstract: Optional[str] = None
    authors: List[AuthorSchema] = []
    publication_date: Optional[str] = None
    journal: Optional[str] = None
    volume: Optional[str] = None
    issue: Optional[str] = None
    pages: Optional[str] = None
    doi: Optional[str] = None
    keywords: List[str] = []
    research_field: Optional[str] = None
    document_type: str
    language: str
    uploaded_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DocumentExistsResponse(BaseModel):
    """文档存在性检查响应"""
    exists: bool
    document: Optional[DocumentResponse] = None


class DocumentUploadResponse(BaseModel):
    """文档上传响应"""
    success: bool
    message: str
    document_id: str


class PricingResponseSchema(BaseModel):
    plans: List[PricingPlanSchema]





# 通用响应模式
class MessageResponseSchema(BaseModel):
    status: str
    message: str


class ErrorResponseSchema(BaseModel):
    detail: str


# 账户页面数据模式
class AccountDataSchema(BaseModel):
    user: UserSchema
    subscription: SubscriptionSchema 