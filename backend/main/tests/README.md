# 测试套件

## 测试概览

本测试套件包含了对项目各个模块的全面测试，确保功能的可靠性和正确性。

## 运行测试

### 方法一：使用 Django 管理命令
```bash
# 运行所有测试
python manage.py test

# 运行特定应用测试
python manage.py test main.tests

# 详细输出模式
python manage.py test --verbosity=2
```

### 方法二：使用测试运行器
```bash
cd backend
python main/tests/test_runner.py
```

## 测试结构

当前测试套件包含：
- 认证相关测试
- 用户管理测试
- 订阅功能测试
- 定价方案测试

## 注意事项

1. **缓存清理**: 每个测试前后都会清理缓存，确保测试独立性
2. **数据库隔离**: 使用 Django 的测试数据库，每个测试后自动回滚
3. **外部依赖**: 所有外部 API 调用都已被 Mock，不会产生实际网络请求
4. **错误处理**: 包含了各种异常情况的测试，确保错误处理的健壮性

## 扩展测试

如果需要添加新的测试用例，请遵循以下命名规范：
- `test_[功能名称]_[场景]`: 如 `test_user_creation_success`
- 使用描述性的 docstring
- 适当使用 Mock 对象
- 验证预期行为和异常情况 