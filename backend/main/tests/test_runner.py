#!/usr/bin/env python
"""
测试运行器
使用方法：
    python manage.py test
    或
    python test_runner.py
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner

if __name__ == "__main__":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'appconfig.settings')
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # 运行所有测试
    failures = test_runner.run_tests([
        "main.tests"
    ])
    
    if failures:
        sys.exit(1) 