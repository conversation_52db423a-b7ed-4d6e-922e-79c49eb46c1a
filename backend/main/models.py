from django.db import models
from django.contrib.auth.models import AbstractUser, UserManager
from django.utils.translation import gettext_lazy as _
import uuid
from django.utils import timezone
from django.db.models.signals import post_save
from django.dispatch import receiver


class CustomUserManager(UserManager):
    def create_superuser(self, phone, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('username', phone)  # 使用手机号作为用户名

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(phone, password, **extra_fields)

    def create_user(self, phone, password=None, **extra_fields):
        if not phone:
            raise ValueError('The Phone field must be set')
        
        extra_fields.setdefault('username', phone)  # 使用手机号作为用户名
        user = self.model(phone=phone, **extra_fields)
        if password:
            user.set_password(password)
        user.save(using=self._db)
        return user


class User(AbstractUser):
    """自定义用户模型"""

    phone = models.CharField(
        _("手机号码"), max_length=11, unique=True, null=True, blank=True
    )
    email = models.EmailField(_("邮箱地址"), unique=True, null=True, blank=True)
    username = models.CharField(
        _("用户名"), max_length=150, unique=True, default="", blank=True
    )

    # 使用手机号作为用户名
    USERNAME_FIELD = "phone"
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    class Meta:
        verbose_name = _("用户")
        verbose_name_plural = _("用户")

    def __str__(self):
        return self.phone or self.email

    def save(self, *args, **kwargs):
        if not self.username:
            self.username = str(uuid.uuid4())[:8]  # 生成随机用户名
        if not self.password:
            self.set_unusable_password()  # 设置不可用的密码
        super().save(*args, **kwargs)


class Subscription(models.Model):
    """用户订阅状态"""

    SUBSCRIPTION_TYPES = (
        ("free", "免费用户"),
        ("premium", "高级用户"),
        ("plus", "专业用户"),
    )

    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="subscription"
    )
    subscription_type = models.CharField(
        _("订阅类型"), max_length=10, choices=SUBSCRIPTION_TYPES, default="free"
    )
    start_date = models.DateTimeField(_("开始时间"), auto_now_add=True)
    end_date = models.DateTimeField(_("结束时间"), null=True, blank=True)
    is_active = models.BooleanField(_("是否激活"), default=True)

    class Meta:
        verbose_name = _("订阅")
        verbose_name_plural = _("订阅")

    def __str__(self):
        return f"{self.user.phone} - {self.get_subscription_type_display()}"

    @property
    def is_premium(self):
        """判断是否为付费用户"""
        return self.subscription_type in ["premium", "plus"] and self.is_active

    @property
    def days_remaining(self):
        """计算剩余天数"""
        if not self.end_date or not self.is_premium:
            return 0
        remaining = self.end_date - timezone.now()
        return max(0, remaining.days)

    @property
    def monthly_document_limit(self):
        """获取每月文档限制数量"""
        if self.subscription_type == "free":
            return 5
        elif self.subscription_type == "premium":
            return 50
        elif self.subscription_type == "plus":
            return -1  # -1 表示无限制
        return 0

    @property
    def current_month_usage(self):
        """获取当前月份的文档使用量（基于唯一文档ID）"""
        return self.get_usage_for_period()

    def get_current_month_usage(self):
        """获取当前月份的文档使用量（方法版本，保持向后兼容）"""
        return self.current_month_usage

    def get_usage_for_period(self, start_date=None, end_date=None):
        """获取指定时间段内的唯一文档使用量"""
        if start_date is None or end_date is None:
            # 默认获取当前订阅周期的使用量
            now = timezone.now()
            if self.subscription_type == "free":
                # 免费用户按自然月计算
                start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                if now.month == 12:
                    end_date = now.replace(year=now.year + 1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
                else:
                    end_date = now.replace(month=now.month + 1, day=1, hour=0, minute=0, second=0, microsecond=0)
            else:
                # 付费用户按订阅周期计算
                start_date = self.start_date
                end_date = self.end_date or now

        # 计算唯一文档ID数量
        unique_docs = DocumentProcessingLog.objects.filter(
            user=self.user,
            created_at__gte=start_date,
            created_at__lt=end_date
        ).values('document_id').distinct().count()

        return unique_docs

    def can_process_document(self, document_id=None):
        """检查是否可以处理文档"""
        if not self.is_active:
            return False

        limit = self.monthly_document_limit
        if limit == -1:  # 无限制
            return True

        # 如果提供了document_id，检查是否已经处理过这个文档
        if document_id:
            if self.has_processed_document(document_id):
                return True  # 已处理过的文档可以重新处理，不计入配额

        current_usage = self.get_current_month_usage()
        return current_usage < limit

    def has_processed_document(self, document_id):
        """检查是否已经处理过指定的文档"""
        return DocumentProcessingLog.objects.filter(
            user=self.user,
            document_id=document_id
        ).exists()

    def log_document_processing(self, document_id, endpoint, content_length=None, processing_time_ms=None):
        """记录文档处理日志"""
        return DocumentProcessingLog.objects.create(
            user=self.user,
            document_id=document_id,
            endpoint=endpoint,
            content_length=content_length,
            processing_time_ms=processing_time_ms
        )


class Document(models.Model):
    """文档存储模型 - 存储完整的科学文献信息"""

    # 使用SHA256哈希作为主键，保持与现有系统的兼容性
    document_id = models.CharField(
        _("文档ID (SHA256)"), max_length=64, primary_key=True,
        help_text="文档内容的SHA256哈希值，作为唯一标识符"
    )

    # 基本文档信息
    title = models.TextField(_("标题"), help_text="文档标题")
    abstract = models.TextField(_("摘要"), blank=True, help_text="文档摘要或概述")
    full_text = models.TextField(_("全文内容"), help_text="完整的文档内容")

    # 作者信息 - 存储为JSON格式以支持多个作者
    authors = models.JSONField(
        _("作者列表"), default=list, blank=True,
        help_text="作者信息列表，格式：[{'name': '作者名', 'affiliation': '机构', 'email': '邮箱'}]"
    )

    # 科学文献元数据
    publication_date = models.DateField(_("发表日期"), null=True, blank=True)
    journal = models.CharField(_("期刊/会议"), max_length=500, blank=True)
    volume = models.CharField(_("卷号"), max_length=50, blank=True)
    issue = models.CharField(_("期号"), max_length=50, blank=True)
    pages = models.CharField(_("页码"), max_length=50, blank=True)
    doi = models.CharField(_("DOI"), max_length=200, blank=True, unique=True, null=True)

    # 分类和标签
    keywords = models.JSONField(
        _("关键词"), default=list, blank=True,
        help_text="关键词列表"
    )
    research_field = models.CharField(_("研究领域"), max_length=200, blank=True)
    document_type = models.CharField(
        _("文档类型"), max_length=50, default="article",
        choices=[
            ("article", "期刊论文"),
            ("conference", "会议论文"),
            ("book", "书籍"),
            ("chapter", "书籍章节"),
            ("thesis", "学位论文"),
            ("report", "研究报告"),
            ("other", "其他")
        ]
    )

    # 语言和格式
    language = models.CharField(_("语言"), max_length=10, default="zh",
                               choices=[("zh", "中文"), ("en", "英文"), ("other", "其他")])

    # 系统字段
    uploaded_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name="uploaded_documents", verbose_name=_("上传用户")
    )
    created_at = models.DateTimeField(_("创建时间"), auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(_("更新时间"), auto_now=True)

    class Meta:
        verbose_name = _("文档")
        verbose_name_plural = _("文档")
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['uploaded_by', 'created_at']),
            models.Index(fields=['research_field']),
            models.Index(fields=['document_type']),
            models.Index(fields=['publication_date']),
        ]

    def __str__(self):
        return f"{self.title[:50]}... ({self.document_id[:8]}...)"

    @property
    def author_names(self):
        """获取作者姓名列表"""
        if isinstance(self.authors, list):
            return [author.get('name', '') for author in self.authors if isinstance(author, dict)]
        return []

    @property
    def author_names_str(self):
        """获取作者姓名字符串（用逗号分隔）"""
        return ", ".join(self.author_names)


class DocumentProcessingLog(models.Model):
    """文档处理日志 - 基于SHA256哈希的使用跟踪"""

    ENDPOINT_CHOICES = (
        ('summarize', '文档摘要'),
        ('literature', '文献分析'),
    )

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="document_processing_logs"
    )
    document_id = models.CharField(
        _("文档ID (SHA256)"), max_length=64, db_index=True,
        help_text="文档内容的SHA256哈希值，用于去重"
    )
    endpoint = models.CharField(
        _("处理端点"), max_length=20, choices=ENDPOINT_CHOICES
    )
    created_at = models.DateTimeField(_("处理时间"), auto_now_add=True, db_index=True)

    # 可选字段用于调试和分析
    content_length = models.IntegerField(_("内容长度"), null=True, blank=True)
    processing_time_ms = models.IntegerField(_("处理时间(毫秒)"), null=True, blank=True)

    class Meta:
        verbose_name = _("文档处理日志")
        verbose_name_plural = _("文档处理日志")
        indexes = [
            # 优化查询性能的复合索引
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['user', 'document_id']),
            models.Index(fields=['user', 'created_at', 'document_id']),
        ]

    def __str__(self):
        return f"{self.user.phone} - {self.endpoint} - {self.document_id[:8]}... - {self.created_at}"


@receiver(post_save, sender=User)
def create_user_subscription(sender, instance, created, **kwargs):
    """当创建新用户时自动创建订阅记录"""
    if created:
        Subscription.objects.create(user=instance)
