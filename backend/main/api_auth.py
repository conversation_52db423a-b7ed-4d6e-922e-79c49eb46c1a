from ninja_extra import api_controller, route
from ninja.errors import HttpError
from django.contrib.auth import get_user_model
from django.core.cache import cache
import random
import string
import re
from datetime import datetime, timedelta

from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_tea_util import models as util_models

from ninja_jwt.tokens import RefreshToken
from ninja_jwt.authentication import JWTAuth

from .schemas import (
    PhoneAuthSchema,
    SendCodeSchema,
    TokenResponseSchema,
    RefreshTokenSchema,
    CheckUserExistsSchema,
    UserExistsResponseSchema,
    MessageResponseSchema,
    UserSchema,
    AccountDataSchema,
    SubscriptionSchema
)

from .models import Subscription

User = get_user_model()

@api_controller("/auth", tags=["认证"])
class AuthController:
    """认证相关API控制器"""

    def generate_verification_code(self):
        """生成6位数字验证码"""
        return "".join(random.choices(string.digits, k=6))

    def verify_code(self, phone: str, code: str) -> bool:
        """验证验证码"""
        cache_key = f"verification_code_{phone}"
        stored_code = cache.get(cache_key)
        return stored_code == code

    @staticmethod
    def send_verification_code_sms(phone: str, code: str):
        """使用阿里云发送短信验证码"""
        config = open_api_models.Config(
            access_key_id="LTAI5tMz3b9AARt9v2b9o2WW",
            access_key_secret="******************************",
        )
        config.endpoint = f'dysmsapi.aliyuncs.com'
        client = Dysmsapi20170525Client(config)

        send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
            phone_numbers=f"86{phone}",
            sign_name="北京昔彦科技",
            template_code="SMS_487420584",
            template_param='{"code": "' + code + '"}',
        )
        runtime = util_models.RuntimeOptions()
        try:
            response = client.send_sms_with_options(send_sms_request, runtime)
            if response.body.code != "OK":
                raise HttpError(400, f"短信发送失败: {response.body.message}")
            return response
        except Exception as error:
            raise HttpError(500, f"短信服务异常: {str(error)}")

    def check_send_frequency(self, phone: str) -> None:
        """检查发送频率限制"""
        # 检查1分钟内是否已发送
        minute_key = f"sms_minute_limit_{phone}"
        if cache.get(minute_key):
            raise HttpError(429, "发送太频繁，请1分钟后再试")
        
        # 检查1小时内发送次数
        hour_key = f"sms_hour_count_{phone}"
        hour_count = cache.get(hour_key, 0)
        if hour_count >= 5:
            raise HttpError(429, "已达到每小时发送限制，请稍后再试")
        
        # 检查24小时内发送次数
        day_key = f"sms_day_count_{phone}"
        day_count = cache.get(day_key, 0)
        if day_count >= 10:
            raise HttpError(429, "已达到每天发送限制，请明天再试")

    def update_send_counters(self, phone: str) -> None:
        """更新发送计数器"""
        # 1分钟限制
        minute_key = f"sms_minute_limit_{phone}"
        cache.set(minute_key, True, 60)
        
        # 小时计数
        hour_key = f"sms_hour_count_{phone}"
        hour_count = cache.get(hour_key, 0)
        cache.set(hour_key, hour_count + 1, 3600)
        
        # 天计数
        day_key = f"sms_day_count_{phone}"
        day_count = cache.get(day_key, 0)
        cache.set(day_key, day_count + 1, 86400)

    @route.post("/send-code", response=MessageResponseSchema)
    def send_code(self, request, data: SendCodeSchema):
        """发送验证码"""
        # 验证手机号格式
        if not re.match(r'^1[3-9]\d{9}$', data.phone):
            raise HttpError(400, "无效的手机号码格式")

        try:
            # 检查发送频率
            self.check_send_frequency(data.phone)
            
            # 生成验证码
            code = self.generate_verification_code()
            cache_key = f"verification_code_{data.phone}"
            
            # 发送验证码
            self.send_verification_code_sms(data.phone, code)
            
            # 保存验证码和更新计数器
            cache.set(cache_key, code, 300)  # 5分钟有效期
            self.update_send_counters(data.phone)
            
            return {"status": "success", "message": "验证码已发送"}
            
        except HttpError:
            raise  # 直接重新抛出 HttpError
        except Exception as e:
            raise HttpError(500, f"发送验证码失败: {str(e)}")

    @route.post("/check-user", response=UserExistsResponseSchema)
    def check_user_exists(self, request, data: CheckUserExistsSchema):
        """检查用户是否存在"""
        user_exists = User.objects.filter(phone=data.phone).exists()
        return {"exists": user_exists}

    @route.post("/login", response=TokenResponseSchema)
    def login_or_register(self, request, data: PhoneAuthSchema):
        """统一登录注册接口"""
        if not self.verify_code(data.phone, data.verification_code):
            raise HttpError(400, "验证码错误或已过期")

        try:
            # 查找或创建用户
            user, created = User.objects.get_or_create(phone=data.phone)
            
            # 查找或创建订阅（如果是新用户）
            if created:
                Subscription.objects.get_or_create(
                    user=user,
                    defaults={
                        "subscription_type": "free",
                        "is_active": True
                    }
                )

            # 使用django-ninja-jwt生成令牌
            refresh = RefreshToken.for_user(user)
            
            return {
                "access_token": str(refresh.access_token),
                "refresh_token": str(refresh),
                "token_type": "bearer",
                "expires_in": int(refresh.access_token.lifetime.total_seconds()),
                "user": UserSchema.model_validate(user)
            }
        except Exception as e:
            raise HttpError(500, f"登录/注册失败: {str(e)}")

    @route.post("/refresh", response=TokenResponseSchema)
    def refresh_token(self, request, data: RefreshTokenSchema):
        """刷新访问令牌"""
        try:
            refresh = RefreshToken(data.refresh_token)
            
            # 获取用户信息
            user_id = refresh.payload.get('user_id')
            user = User.objects.get(id=user_id)
            
            # 生成新的访问令牌
            new_access_token = refresh.access_token
            
            return {
                "access_token": str(new_access_token),
                "refresh_token": str(refresh),  # 保持原有的refresh token
                "token_type": "bearer", 
                "expires_in": int(new_access_token.lifetime.total_seconds()),
                "user": UserSchema.from_orm(user)
            }
        except Exception as e:
            raise HttpError(401, "无效的刷新令牌")

    @route.get("/me", response=AccountDataSchema, auth=JWTAuth())
    def get_user_profile(self, request):
        """获取用户信息"""
        user = request.auth
        return {
            "user": UserSchema.model_validate(user),
            "subscription": SubscriptionSchema.model_validate(user.subscription)
        } 