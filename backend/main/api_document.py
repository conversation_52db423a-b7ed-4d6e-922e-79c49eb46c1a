from ninja import Schema
from ninja_extra import api_controller, route
from ninja_extra.permissions import IsAuthenticated
from ninja.errors import HttpError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime
import hashlib
import re

from .models import Document
from .schemas import (
    DocumentUploadRequest, 
    DocumentValidationRequest, 
    DocumentResponse, 
    DocumentExistsResponse, 
    DocumentUploadResponse,
    AuthorSchema
)

User = get_user_model()


class Error(Schema):
    message: str


@api_controller("/documents", tags=["Documents"])
class DocumentController:
    """文档管理控制器"""

    @route.post("/upload", response={200: DocumentUploadResponse, 400: Error})
    def upload_document(self, request, payload: DocumentUploadRequest):
        """上传文档信息"""
        try:
            user = None  # 暂时不需要认证用户
            
            # 验证document_id是否与内容匹配
            calculated_hash = hashlib.sha256(payload.full_text.encode('utf-8')).hexdigest()
            if calculated_hash != payload.document_id:
                return 400, {"message": f"文档ID与内容不匹配。计算得到的哈希值: {calculated_hash}"}
            
            # 检查文档是否已存在
            if Document.objects.filter(document_id=payload.document_id).exists():
                return 400, {"message": "文档已存在，无需重复上传"}
            
            # 处理发表日期
            publication_date = None
            if payload.publication_date:
                try:
                    publication_date = datetime.strptime(payload.publication_date, "%Y-%m-%d").date()
                except ValueError:
                    return 400, {"message": "发表日期格式错误，请使用 YYYY-MM-DD 格式"}
            
            # 转换作者信息为JSON格式
            authors_json = []
            if payload.authors:
                for author in payload.authors:
                    authors_json.append({
                        "name": author.name,
                        "affiliation": author.affiliation or "",
                        "email": author.email or ""
                    })
            
            # 创建文档记录
            document = Document.objects.create(
                document_id=payload.document_id,
                title=payload.title,
                abstract=payload.abstract or "",
                full_text=payload.full_text,
                authors=authors_json,
                publication_date=publication_date,
                journal=payload.journal or "",
                volume=payload.volume or "",
                issue=payload.issue or "",
                pages=payload.pages or "",
                doi=payload.doi or None,
                keywords=payload.keywords or [],
                research_field=payload.research_field or "",
                document_type=payload.document_type or "article",
                language=payload.language or "zh",
                uploaded_by=user
            )
            
            return 200, {
                "success": True,
                "message": "文档上传成功",
                "document_id": document.document_id
            }
            
        except Exception as e:
            return 400, {"message": f"上传失败: {str(e)}"}

    @route.post("/validate", response={200: DocumentExistsResponse, 400: Error})
    def validate_document(self, request, payload: DocumentValidationRequest):
        """验证文档是否存在"""
        try:
            document = Document.objects.filter(document_id=payload.document_id).first()
            
            if document:
                # 转换作者信息
                authors = []
                if document.authors:
                    for author_data in document.authors:
                        authors.append(AuthorSchema(
                            name=author_data.get('name', ''),
                            affiliation=author_data.get('affiliation') or None,
                            email=author_data.get('email') or None
                        ))
                
                document_response = DocumentResponse(
                    document_id=document.document_id,
                    title=document.title,
                    abstract=document.abstract or None,
                    authors=authors,
                    publication_date=document.publication_date.isoformat() if document.publication_date else None,
                    journal=document.journal or None,
                    volume=document.volume or None,
                    issue=document.issue or None,
                    pages=document.pages or None,
                    doi=document.doi or None,
                    keywords=document.keywords or [],
                    research_field=document.research_field or None,
                    document_type=document.document_type,
                    language=document.language,
                    uploaded_by=document.uploaded_by.phone if document.uploaded_by else None,
                    created_at=document.created_at,
                    updated_at=document.updated_at
                )
                
                return 200, {
                    "exists": True,
                    "document": document_response
                }
            else:
                return 200, {
                    "exists": False,
                    "document": None
                }
                
        except Exception as e:
            return 400, {"message": f"验证失败: {str(e)}"}

    @route.get("/{document_id}", response={200: DocumentResponse, 404: Error, 400: Error})
    def get_document(self, request, document_id: str):
        """根据document_id获取文档详细信息"""
        try:
            # 验证SHA256格式
            if not re.match(r'^[a-fA-F0-9]{64}$', document_id):
                return 400, {"message": "无效的文档ID格式"}
            
            document = Document.objects.filter(document_id=document_id).first()
            
            if not document:
                return 404, {"message": "文档不存在"}
            
            # 转换作者信息
            authors = []
            if document.authors:
                for author_data in document.authors:
                    authors.append(AuthorSchema(
                        name=author_data.get('name', ''),
                        affiliation=author_data.get('affiliation') or None,
                        email=author_data.get('email') or None
                    ))
            
            document_response = DocumentResponse(
                document_id=document.document_id,
                title=document.title,
                abstract=document.abstract or None,
                authors=authors,
                publication_date=document.publication_date.isoformat() if document.publication_date else None,
                journal=document.journal or None,
                volume=document.volume or None,
                issue=document.issue or None,
                pages=document.pages or None,
                doi=document.doi or None,
                keywords=document.keywords or [],
                research_field=document.research_field or None,
                document_type=document.document_type,
                language=document.language,
                uploaded_by=document.uploaded_by.phone if document.uploaded_by else None,
                created_at=document.created_at,
                updated_at=document.updated_at
            )
            
            return 200, document_response
            
        except Exception as e:
            return 400, {"message": f"获取文档失败: {str(e)}"}
