from ninja import Schema
from ninja_extra import api_controller, route
from django.conf import settings
import os
from openai import OpenAI
from typing import Dict, Any, Optional, AsyncGenerator
from enum import Enum
from django.http import StreamingHttpResponse
import json
from django.contrib.auth import get_user_model
from ninja_extra.permissions import IsAuthenticated
from ninja.errors import HttpError

User = get_user_model()

# 导入Document模型
from main.models import Document


class LLMModel(str, Enum):
    QWEN_TURBO = "qwen-turbo"
    QWEN_PLUS = "qwen-plus"
    QWEN_MAX = "qwen-max"


class ChatRequest(Schema):
    prompt: str


class SummaryRequest(Schema):
    document_id: str  # SHA256 hash of the document content (64 hex characters)
    max_length: Optional[int] = 800  # 增加默认长度以支持更详细的摘要

    def __init__(self, **data):
        super().__init__(**data)
        # Validate SHA256 format
        if not self._is_valid_sha256(self.document_id):
            raise ValueError("document_id must be a valid SHA256 hash (64 hexadecimal characters)")

    @staticmethod
    def _is_valid_sha256(hash_string: str) -> bool:
        """Validate if string is a valid SHA256 hash"""
        import re
        return bool(re.match(r'^[a-fA-F0-9]{64}$', hash_string))


class TranslateRequest(Schema):
    text: str
    target_language: str = "中文"
    source_language: Optional[str] = None


class MindMapRequest(Schema):
    text: str
    topic: Optional[str] = None


class ConceptRequest(Schema):
    concept: str
    context: Optional[str] = None


class LiteratureRequest(Schema):
    document_id: str  # SHA256 hash of the document content (64 hex characters)
    field: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        # Validate SHA256 format
        if not self._is_valid_sha256(self.document_id):
            raise ValueError("document_id must be a valid SHA256 hash (64 hexadecimal characters)")

    @staticmethod
    def _is_valid_sha256(hash_string: str) -> bool:
        """Validate if string is a valid SHA256 hash"""
        import re
        return bool(re.match(r'^[a-fA-F0-9]{64}$', hash_string))


class Error(Schema):
    message: str


class StreamResponse:
    """流式响应的格式化类"""
    @staticmethod
    def format_chunk(text: str, done: bool = False) -> str:
        response = {
            "text": text,
            "done": done
        }
        return f"data: {json.dumps(response, ensure_ascii=False)}\n\n"


class LLMService:
    """LLM服务抽象类，用于统一处理不同的LLM调用"""

    @staticmethod
    def _get_client() -> OpenAI:
        """获取OpenAI客户端实例"""
        api_key = getattr(settings, "DASHSCOPE_API_KEY", None) or os.getenv("DASHSCOPE_API_KEY")
        if not api_key:
            raise ValueError("DASHSCOPE_API_KEY not configured")
        
        return OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )

    @staticmethod
    def call_llm_sync(
        prompt: str,
        model: LLMModel = LLMModel.QWEN_TURBO,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
        stream: bool = False,
    ):
        """
        同步版本的LLM调用函数
        """
        try:
            client = LLMService._get_client()

            # 构建消息格式
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # 构建请求参数
            completion_kwargs = {
                "model": model.value,
                "messages": messages,
                "temperature": temperature,
                "stream": stream,
            }

            if max_tokens:
                completion_kwargs["max_tokens"] = max_tokens

            # 调用API
            completion = client.chat.completions.create(**completion_kwargs)

            if stream:
                def generate_stream():
                    for chunk in completion:
                        if chunk.choices and chunk.choices[0].delta.content:
                            yield StreamResponse.format_chunk(chunk.choices[0].delta.content)
                    yield StreamResponse.format_chunk("", done=True)

                return generate_stream()
            else:
                # 非流式输出处理
                return {
                    "output": {
                        "text": completion.choices[0].message.content
                    },
                    "usage": {
                        "output_tokens": completion.usage.completion_tokens if completion.usage else 0,
                        "input_tokens": completion.usage.prompt_tokens if completion.usage else 0,
                        "total_tokens": completion.usage.total_tokens if completion.usage else 0
                    },
                    "request_id": completion.id if hasattr(completion, 'id') else "non_stream_request"
                }

        except Exception as e:
            raise Exception(f"Error calling Qwen API: {str(e)}")

    @staticmethod
    async def call_llm(
        prompt: str,
        model: LLMModel = LLMModel.QWEN_TURBO,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
        stream: bool = False,
    ) -> AsyncGenerator[str, None] | Dict[str, Any]:
        """
        通用的LLM调用函数

        Args:
            prompt: 用户输入的提示词
            model: 使用的模型
            temperature: 温度参数，控制输出的随机性
            max_tokens: 最大输出token数
            system_prompt: 系统提示词
            stream: 是否使用流式输出

        Returns:
            如果 stream=True，返回一个异步生成器，生成流式响应
            如果 stream=False，返回完整的响应字典
        """
        try:
            client = LLMService._get_client()
            
            # 构建消息格式
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # 构建请求参数
            completion_kwargs = {
                "model": model.value,
                "messages": messages,
                "temperature": temperature,
                "stream": stream,
            }
            
            if max_tokens:
                completion_kwargs["max_tokens"] = max_tokens

            # 调用API
            completion = client.chat.completions.create(**completion_kwargs)
            
            if stream:
                async def generate_stream():
                    for chunk in completion:
                        if chunk.choices and chunk.choices[0].delta.content:
                            yield StreamResponse.format_chunk(chunk.choices[0].delta.content)
                    yield StreamResponse.format_chunk("", done=True)
                
                return generate_stream()
            else:
                # 非流式输出处理
                return {
                    "output": {
                        "text": completion.choices[0].message.content
                    },
                    "usage": {
                        "output_tokens": completion.usage.completion_tokens if completion.usage else 0,
                        "input_tokens": completion.usage.prompt_tokens if completion.usage else 0,
                        "total_tokens": completion.usage.total_tokens if completion.usage else 0
                    },
                    "request_id": completion.id if hasattr(completion, 'id') else "non_stream_request"
                }
                
        except Exception as e:
            raise Exception(f"Error calling Qwen API: {str(e)}")


@api_controller("/llm", tags=["LLM"])
class LLMController:

    @route.post("/chat", response={200: dict, 400: Error})
    async def chat(self, request, payload: ChatRequest):
        """通用聊天接口"""
        try:
            stream_response = await LLMService.call_llm(
                prompt=payload.prompt,
                model=LLMModel.QWEN_TURBO,
                stream=True
            )
            return StreamingHttpResponse(
                stream_response,
                content_type='text/event-stream'
            )
        except Exception as e:
            return 400, {"message": str(e)}

    @route.post("/summarize", response={200: dict, 400: Error})
    def summarize(self, request, payload: SummaryRequest):
        """文本摘要接口"""
        import time
        start_time = time.time()

        # 暂时跳过用户认证和订阅检查用于测试
        # user = request.auth
        # if not hasattr(user, 'subscription'):
        #     raise HttpError(400, "用户订阅信息不存在")
        # subscription = user.subscription
        # if not subscription.can_process_document(payload.document_id):
        #     limit = subscription.monthly_document_limit
        #     current_usage = subscription.get_current_month_usage()
        #     if limit == -1:
        #         raise HttpError(403, "订阅已过期，无法处理文档")
        #     else:
        #         raise HttpError(403, f"已达到本月文档处理限制 ({current_usage}/{limit})，请升级订阅")

        # 从数据库获取文档信息
        try:
            document = Document.objects.get(document_id=payload.document_id)
        except Document.DoesNotExist:
            raise HttpError(404, "文档不存在，请先上传文档信息")

        # 暂时跳过处理检查用于测试
        # already_processed = subscription.has_processed_document(payload.document_id)

        # 调整默认长度，如果用户没有指定则使用更长的默认值
        target_length = payload.max_length if payload.max_length and payload.max_length > 200 else 800

        # 构建包含文档元数据的系统提示
        document_info = f"""文档信息：
标题：{document.title}
作者：{document.author_names_str}
期刊：{document.journal}
研究领域：{document.research_field}
关键词：{', '.join(document.keywords) if document.keywords else '无'}
"""

        system_prompt = f"""你是一个专业的学术文献摘要助手。请为用户提供的文本生成详细、结构化的摘要。

{document_info}

摘要要求：
1. 保持原文的核心信息和主要观点，不遗漏重要内容
2. 按照学术论文的标准结构组织内容，包括但不限于：
   - **引言/背景 (Introduction/Background)**：研究背景、问题陈述、研究意义
   - **方法 (Methods/Methodology)**：研究方法、实验设计、数据来源
   - **结果 (Results/Findings)**：主要发现、关键数据、重要结论
   - **讨论 (Discussion)**：结果分析、理论意义、实际应用
   - **结论 (Conclusion)**：总结要点、研究贡献、未来展望
3. 每个部分应该有足够的细节，确保读者能够理解研究的完整脉络
4. 使用清晰的标题和段落结构，便于阅读
5. 摘要总长度控制在{target_length}字左右，可以适当超出以保证内容完整性
6. 使用中文输出，保持学术写作的专业性和准确性
7. 如果原文不是学术论文，请根据内容特点调整结构（如：概述、主要内容、关键观点、总结等）"""

        prompt = f"""请为以下文本生成详细的结构化摘要：

{document.full_text}

请按照学术论文的标准结构，生成一个详细且完整的摘要，确保涵盖原文的所有重要信息。"""

        try:
            stream_response = LLMService.call_llm_sync(
                prompt=prompt,
                system_prompt=system_prompt,
                model=LLMModel.QWEN_PLUS,
                temperature=0.3,
                max_tokens=target_length * 3,  # 给更多token空间以确保完整输出
                stream=True
            )

            # 暂时跳过日志记录用于测试
            # processing_time_ms = int((time.time() - start_time) * 1000)
            # subscription.log_document_processing(
            #     document_id=payload.document_id,
            #     endpoint='summarize',
            #     content_length=len(document.full_text),
            #     processing_time_ms=processing_time_ms
            # )

            return StreamingHttpResponse(
                stream_response,
                content_type='text/event-stream'
            )
        except Exception as e:
            return 400, {"message": str(e)}

    @route.post("/translate", response={200: dict, 400: Error})
    async def translate(self, request, payload: TranslateRequest):
        """文本翻译接口"""
        system_prompt = f"""你是一个专业的翻译助手。请将用户提供的文本翻译成{payload.target_language}。
翻译要求：
1. 保持原文的意思和语调
2. 使用自然、流畅的表达
3. 注意专业术语的准确翻译
4. 保持格式和结构"""

        source_lang_hint = (
            f"（原文语言：{payload.source_language}）"
            if payload.source_language
            else ""
        )
        prompt = f"请将以下文本翻译成{payload.target_language}{source_lang_hint}：\n\n{payload.text}"

        try:
            stream_response = await LLMService.call_llm(
                prompt=prompt,
                system_prompt=system_prompt,
                model=LLMModel.QWEN_PLUS,
                temperature=0.2,
                stream=True
            )
            return StreamingHttpResponse(
                stream_response,
                content_type='text/event-stream'
            )
        except Exception as e:
            return 400, {"message": str(e)}

    @route.post("/mindmap", response={200: dict, 400: Error})
    async def summary_to_mindmap(self, request, payload: MindMapRequest):
        """摘要转思维导图接口"""
        topic_hint = f"主题：{payload.topic}\n" if payload.topic else ""

        system_prompt = """你是一个专业的思维导图生成助手。请将用户提供的文本内容转换为结构化的思维导图格式。
输出要求：
1. 使用Markdown格式的层级结构
2. 主题作为一级标题
3. 主要分支作为二级标题
4. 详细内容作为三级及以下标题
5. 保持逻辑清晰，层次分明
6. 使用中文输出"""

        prompt = f"""请将以下内容转换为思维导图格式：
{topic_hint}
内容：
{payload.text}

请生成结构化的思维导图，使用Markdown格式输出。"""

        try:
            stream_response = await LLMService.call_llm(
                prompt=prompt,
                system_prompt=system_prompt,
                model=LLMModel.QWEN_PLUS,
                temperature=0.4,
                stream=True
            )
            return StreamingHttpResponse(
                stream_response,
                content_type='text/event-stream'
            )
        except Exception as e:
            return 400, {"message": str(e)}

    @route.post("/concept", response={200: dict, 400: Error})
    async def explain_concept(self, request, payload: ConceptRequest):
        """概念解释接口"""
        context_hint = f"上下文：{payload.context}\n" if payload.context else ""

        system_prompt = """你是一个专业的概念解释助手。请为用户提供清晰、准确、易懂的概念解释。
解释要求：
1. 定义准确，表述清晰
2. 提供具体例子帮助理解
3. 如有必要，说明相关概念的区别和联系
4. 使用通俗易懂的语言
5. 结构化输出，包含定义、特点、应用等方面
6. 使用中文输出"""

        prompt = f"""请解释以下概念：
概念：{payload.concept}
{context_hint}
请提供详细、准确的解释，包括定义、特点、应用场景等。"""

        try:
            stream_response = await LLMService.call_llm(
                prompt=prompt,
                system_prompt=system_prompt,
                model=LLMModel.QWEN_PLUS,
                temperature=0.3,
                stream=True
            )
            return StreamingHttpResponse(
                stream_response,
                content_type='text/event-stream'
            )
        except Exception as e:
            return 400, {"message": str(e)}

    @route.post("/literature", response={200: dict, 400: Error})
    def analyze_literature_relations(self, request, payload: LiteratureRequest):
        """文献链接关系分析接口"""
        import time
        start_time = time.time()

        # 暂时跳过用户认证和订阅检查用于测试
        # user = request.auth
        # if not hasattr(user, 'subscription'):
        #     raise HttpError(400, "用户订阅信息不存在")
        # subscription = user.subscription
        # if not subscription.can_process_document(payload.document_id):
        #     limit = subscription.monthly_document_limit
        #     current_usage = subscription.get_current_month_usage()
        #     if limit == -1:
        #         raise HttpError(403, "订阅已过期，无法处理文档")
        #     else:
        #         raise HttpError(403, f"已达到本月文档处理限制 ({current_usage}/{limit})，请升级订阅")

        # 从数据库获取文档信息
        try:
            document = Document.objects.get(document_id=payload.document_id)
        except Document.DoesNotExist:
            raise HttpError(404, "文档不存在，请先上传文档信息")

        # 使用文档的研究领域信息，如果用户没有指定的话
        field = payload.field or document.research_field
        field_hint = f"研究领域：{field}\n" if field else ""

        # 构建包含文档元数据的系统提示
        document_info = f"""文档信息：
标题：{document.title}
作者：{document.author_names_str}
期刊：{document.journal}
研究领域：{document.research_field}
关键词：{', '.join(document.keywords) if document.keywords else '无'}
"""

        system_prompt = f"""你是一个专业的文献分析助手。请分析文献中的理论、概念、方法之间的关系。

{document_info}

分析要求：
1. 识别文献中的核心理论框架和概念
2. 分析不同概念之间的逻辑关系和层次结构
3. 梳理研究方法与理论的对应关系
4. 识别文献的创新点和贡献
5. 分析与相关研究的联系和区别
6. 提供结构化的关系图谱描述
7. 使用中文输出，保持学术分析的专业性"""

        prompt = f"""请分析以下文献内容中的理论、概念、方法关系：
{field_hint}
文献内容：
{document.full_text}

请分析其中的理论、概念、方法之间的关系，并提供结构化的分析结果。"""

        try:
            stream_response = LLMService.call_llm_sync(
                prompt=prompt,
                system_prompt=system_prompt,
                model=LLMModel.QWEN_MAX,
                temperature=0.4,
                stream=True
            )

            # 暂时跳过日志记录用于测试
            # processing_time_ms = int((time.time() - start_time) * 1000)
            # subscription.log_document_processing(
            #     document_id=payload.document_id,
            #     endpoint='literature',
            #     content_length=len(document.full_text),
            #     processing_time_ms=processing_time_ms
            # )

            return StreamingHttpResponse(
                stream_response,
                content_type='text/event-stream'
            )
        except Exception as e:
            return 400, {"message": str(e)}
