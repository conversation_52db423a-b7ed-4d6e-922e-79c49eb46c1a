"""
Django settings for appconfig project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os
from datetime import timedelta
from dotenv import load_dotenv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 加载 .env 文件
load_dotenv(dotenv_path=BASE_DIR / 'secrets.env')

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',  # 添加 admin
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',  # 添加 messages
    'django.contrib.staticfiles',
    'corsheaders',  # 添加CORS支持
    'ninja',  # 添加django-ninja
    'ninja_jwt',  # 添加django-ninja-jwt
    'ninja_extra',  # 添加django-ninja-extra
    'main',
    'llm_proxy',
    'payment',  # 添加支付应用
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # CORS中间件，需要放在最前面
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',  # 添加 messages 中间件
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'appconfig.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',  # 添加 messages context processor
            ],
        },
    },
]

WSGI_APPLICATION = 'appconfig.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME', 'z_aiden_db'),
        'USER': os.getenv('DB_USER', 'postgres'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'your_password'),
        'HOST': os.getenv('DB_HOST', 'localhost'),
        'PORT': os.getenv('DB_PORT', '5432'),
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = True


# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 自定义用户模型
AUTH_USER_MODEL = 'main.User'

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    }
}

# CORS 配置
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # Vue开发服务器
    "http://127.0.0.1:3000",
    "http://localhost:5173",  # Vite开发服务器
    "http://127.0.0.1:5173",
]

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_ALL_ORIGINS = DEBUG  # 开发环境允许所有源

# Django Ninja JWT 配置
NINJA_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'UPDATE_LAST_LOGIN': True,
    
    'SIGNING_KEY': SECRET_KEY,
    'ALGORITHM': 'HS256',
    'VERIFYING_KEY': None,
    'AUDIENCE': None,
    'ISSUER': None,
    'JWK_URL': None,
    'LEEWAY': 0,
    
    # Token 内容配置
    'AUTH_TOKEN_CLASSES': (
        'ninja_jwt.tokens.AccessToken',
    ),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'TOKEN_USER_CLASS': 'ninja_jwt.models.TokenUser',
    
    # 用户身份验证
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'USER_AUTHENTICATION_RULE': 'ninja_jwt.authentication.default_user_authentication_rule',
    
    # Token 前缀
    'AUTH_HEADER_TYPES': ('Bearer',),
    'AUTH_HEADER_NAME': 'HTTP_AUTHORIZATION',
}

# 网站配置
SITE_URL = 'http://localhost:3000'  # 前端网站URL

# 阿里云百炼大模型 API KEY (建议存储在环境变量中)
DASHSCOPE_API_KEY = os.getenv('ALIYUN_BAILIAN_API_KEY')

# 邮件配置
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.example.com'  # 替换为您的SMTP服务器
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # 替换为您的邮箱
EMAIL_HOST_PASSWORD = 'your-password'  # 替换为您的密码
DEFAULT_FROM_EMAIL = 'Z-Aiden <<EMAIL>>'  # 替换为您的邮箱

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Antom 支付配置
# 环境切换：设置 ANTOM_ENVIRONMENT 为 'sandbox' 或 'production'
ANTOM_ENVIRONMENT = os.getenv('ANTOM_ENVIRONMENT', 'sandbox')

# 沙盒环境配置
ANTOM_SANDBOX_CLIENT_ID = os.getenv('ANTOM_SANDBOX_CLIENT_ID', 'sandbox_client_id')
ANTOM_SANDBOX_PUBLIC_KEY = os.getenv('ANTOM_SANDBOX_PUBLIC_KEY', 'sandbox_public_key')
ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY = os.getenv('ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY', 'sandbox_private_key')
ANTOM_SANDBOX_GATEWAY_URL = os.getenv('ANTOM_SANDBOX_GATEWAY_URL', 'https://open-sea-global.alipay.com')
ANTOM_SANDBOX_NOTIFY_URL = os.getenv('ANTOM_SANDBOX_NOTIFY_URL', 'http://localhost:8000/api/payment/receiveNotify')
ANTOM_SANDBOX_REDIRECT_URL = os.getenv('ANTOM_SANDBOX_REDIRECT_URL', 'http://localhost:5173/payment/result')

# 生产环境配置
ANTOM_PRODUCTION_CLIENT_ID = os.getenv('ANTOM_PRODUCTION_CLIENT_ID', 'production_client_id')
ANTOM_PRODUCTION_PUBLIC_KEY = os.getenv('ANTOM_PRODUCTION_PUBLIC_KEY', 'production_public_key')
ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY = os.getenv('ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY', 'production_private_key')
ANTOM_PRODUCTION_GATEWAY_URL = os.getenv('ANTOM_PRODUCTION_GATEWAY_URL', 'https://open-global.alipay.com')
ANTOM_PRODUCTION_NOTIFY_URL = os.getenv('ANTOM_PRODUCTION_NOTIFY_URL', 'https://yourdomain.com/api/payment/receiveNotify')
ANTOM_PRODUCTION_REDIRECT_URL = os.getenv('ANTOM_PRODUCTION_REDIRECT_URL', 'https://yourdomain.com/payment/result')

# 根据环境自动选择配置
if ANTOM_ENVIRONMENT == 'production':
    ANTOM_CLIENT_ID = ANTOM_PRODUCTION_CLIENT_ID
    ANTOM_PUBLIC_KEY = ANTOM_PRODUCTION_PUBLIC_KEY
    ANTOM_MERCHANT_PRIVATE_KEY = ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY
    ANTOM_GATEWAY_URL = ANTOM_PRODUCTION_GATEWAY_URL
    ANTOM_NOTIFY_URL = ANTOM_PRODUCTION_NOTIFY_URL
    ANTOM_REDIRECT_URL = ANTOM_PRODUCTION_REDIRECT_URL
else:
    # 默认使用沙盒环境
    ANTOM_CLIENT_ID = ANTOM_SANDBOX_CLIENT_ID
    ANTOM_PUBLIC_KEY = ANTOM_SANDBOX_PUBLIC_KEY
    ANTOM_MERCHANT_PRIVATE_KEY = ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY
    ANTOM_GATEWAY_URL = ANTOM_SANDBOX_GATEWAY_URL
    ANTOM_NOTIFY_URL = ANTOM_SANDBOX_NOTIFY_URL
    ANTOM_REDIRECT_URL = ANTOM_SANDBOX_REDIRECT_URL

# 当前使用的环境信息（用于日志和调试）
ANTOM_CURRENT_ENVIRONMENT = {
    'environment': ANTOM_ENVIRONMENT,
    'gateway_url': ANTOM_GATEWAY_URL,
    'client_id': ANTOM_CLIENT_ID[:8] + '...' if ANTOM_CLIENT_ID else 'Not Set',  # 只显示前8位用于调试
}
