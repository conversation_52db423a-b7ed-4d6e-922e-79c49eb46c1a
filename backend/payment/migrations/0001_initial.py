# Generated by Django 5.2.4 on 2025-08-07 01:29

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, help_text='支付金额（原始单位）', max_digits=10, verbose_name='支付金额')),
                ('currency', models.CharField(choices=[('USD', '美元'), ('CNY', '人民币'), ('EUR', '欧元'), ('GBP', '英镑')], default='USD', max_length=3, verbose_name='货币类型')),
                ('membership_tier', models.CharField(choices=[('free', '免费版'), ('premium', '高级版'), ('plus', '专业版')], max_length=10, verbose_name='会员等级')),
                ('status', models.CharField(choices=[('created', '已创建'), ('pending', '待支付'), ('processing', '处理中'), ('success', '支付成功'), ('failed', '支付失败'), ('cancelled', '已取消'), ('refunded', '已退款')], default='created', max_length=20, verbose_name='支付状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='支付时间')),
                ('antom_payment_request_id', models.CharField(blank=True, help_text='Antom返回的支付请求ID', max_length=255, null=True, verbose_name='Antom支付请求ID')),
                ('antom_session_id', models.CharField(blank=True, max_length=255, null=True, verbose_name='Antom会话ID')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='存储额外的支付相关信息', verbose_name='元数据')),
                ('subscription_start_date', models.DateTimeField(blank=True, null=True, verbose_name='订阅开始时间')),
                ('subscription_end_date', models.DateTimeField(blank=True, null=True, verbose_name='订阅结束时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '支付记录',
                'verbose_name_plural': '支付记录',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'status'], name='payment_pay_user_id_94fc9f_idx'), models.Index(fields=['antom_payment_request_id'], name='payment_pay_antom_p_defc60_idx'), models.Index(fields=['created_at'], name='payment_pay_created_671024_idx'), models.Index(fields=['status'], name='payment_pay_status_124d3d_idx')],
            },
        ),
    ]
