"""
Tests for payment functionality
"""
import json
import uuid
from decimal import Decimal
from unittest.mock import patch, MagicMock

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from ninja_jwt.tokens import RefreshToken

from main.models import User, Subscription
from .models import Payment
from .services import PaymentService, PaymentValidationService


class PaymentModelTest(TestCase):
    """Test Payment model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(phone="13800138000")
        
    def test_payment_creation(self):
        """Test payment record creation"""
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='created'
        )
        
        self.assertEqual(payment.user, self.user)
        self.assertEqual(payment.amount, Decimal('29.00'))
        self.assertEqual(payment.currency, 'USD')
        self.assertEqual(payment.membership_tier, 'premium')
        self.assertEqual(payment.status, 'created')
        self.assertIsNotNone(payment.id)
        self.assertIsNotNone(payment.created_at)
    
    def test_payment_properties(self):
        """Test payment model properties"""
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='created'
        )
        
        # Test amount_in_cents
        self.assertEqual(payment.amount_in_cents, 2900)
        
        # Test is_pending
        self.assertTrue(payment.is_pending)
        
        # Test is_successful
        self.assertFalse(payment.is_successful)
        
        # Update to success and test again
        payment.status = 'success'
        payment.save()
        
        self.assertFalse(payment.is_pending)
        self.assertTrue(payment.is_successful)
        self.assertIsNotNone(payment.paid_at)
    
    def test_calculate_subscription_dates(self):
        """Test subscription date calculation"""
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='success'
        )
        
        start_date, end_date = payment.calculate_subscription_dates()
        
        self.assertIsNotNone(start_date)
        self.assertIsNotNone(end_date)
        self.assertGreater(end_date, start_date)


class PaymentServiceTest(TestCase):
    """Test PaymentService functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(phone="13800138000")
    
    def test_create_payment_record(self):
        """Test payment record creation service"""
        payment = PaymentService.create_payment_record(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium'
        )
        
        self.assertEqual(payment.user, self.user)
        self.assertEqual(payment.amount, Decimal('29.00'))
        self.assertEqual(payment.status, 'created')
    
    def test_create_payment_record_validation(self):
        """Test payment record creation validation"""
        # Test negative amount
        with self.assertRaises(ValueError):
            PaymentService.create_payment_record(
                user=self.user,
                amount=Decimal('-10.00'),
                currency='USD',
                membership_tier='premium'
            )
        
        # Test invalid currency
        with self.assertRaises(ValueError):
            PaymentService.create_payment_record(
                user=self.user,
                amount=Decimal('29.00'),
                currency='INVALID',
                membership_tier='premium'
            )
        
        # Test invalid tier
        with self.assertRaises(ValueError):
            PaymentService.create_payment_record(
                user=self.user,
                amount=Decimal('29.00'),
                currency='USD',
                membership_tier='invalid'
            )
    
    def test_update_payment_status(self):
        """Test payment status update service"""
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='created'
        )
        
        updated_payment = PaymentService.update_payment_status(
            payment_id=str(payment.id),
            status='success',
            metadata={'test': 'data'}
        )
        
        self.assertEqual(updated_payment.status, 'success')
        self.assertIsNotNone(updated_payment.paid_at)
        self.assertEqual(updated_payment.metadata['test'], 'data')
    
    def test_invalid_status_transition(self):
        """Test invalid status transitions"""
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='success'
        )
        
        # Try to transition from success to pending (invalid)
        with self.assertRaises(ValueError):
            PaymentService.update_payment_status(
                payment_id=str(payment.id),
                status='pending'
            )
    
    def test_update_user_subscription(self):
        """Test subscription update service"""
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='success'
        )
        
        subscription = PaymentService.update_user_subscription(payment)
        
        self.assertEqual(subscription.user, self.user)
        self.assertEqual(subscription.subscription_type, 'premium')
        self.assertTrue(subscription.is_active)
        self.assertIsNotNone(subscription.start_date)
        self.assertIsNotNone(subscription.end_date)


class PaymentValidationServiceTest(TestCase):
    """Test PaymentValidationService functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(phone="13800138000")
    
    def test_validate_payment_amount(self):
        """Test payment amount validation"""
        # Valid amounts
        self.assertTrue(PaymentValidationService.validate_payment_amount(29.0, 'premium'))
        self.assertTrue(PaymentValidationService.validate_payment_amount(99.0, 'plus'))
        self.assertTrue(PaymentValidationService.validate_payment_amount(0.0, 'free'))
        
        # Invalid amounts
        self.assertFalse(PaymentValidationService.validate_payment_amount(50.0, 'premium'))
        self.assertFalse(PaymentValidationService.validate_payment_amount(29.0, 'plus'))
    
    def test_validate_user_eligibility(self):
        """Test user eligibility validation"""
        # User with no subscription should be eligible
        is_eligible, reason = PaymentValidationService.validate_user_eligibility(self.user, 'premium')
        self.assertTrue(is_eligible)

        # User should be eligible for upgrade (subscription is auto-created)
        is_eligible, reason = PaymentValidationService.validate_user_eligibility(self.user, 'premium')
        self.assertTrue(is_eligible)

        # Update to premium
        subscription = self.user.subscription
        subscription.subscription_type = 'premium'
        subscription.save()

        # User should not be eligible for same tier
        is_eligible, reason = PaymentValidationService.validate_user_eligibility(self.user, 'premium')
        self.assertFalse(is_eligible)


class PaymentAPITest(TestCase):
    """Test Payment API endpoints"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(phone="13800138000")
        self.refresh = RefreshToken.for_user(self.user)
        self.access_token = str(self.refresh.access_token)
        
    def get_auth_headers(self):
        """Get authentication headers"""
        return {'HTTP_AUTHORIZATION': f'Bearer {self.access_token}'}
    
    def test_create_payment_record_api(self):
        """Test payment record creation API"""
        data = {
            'amount': 29.0,
            'currency': 'USD',
            'membership_tier': 'premium'
        }
        
        response = self.client.post(
            '/api/payment/create',
            data=json.dumps(data),
            content_type='application/json',
            **self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertIn('payment_id', response_data)
        self.assertEqual(response_data['amount'], 29.0)
        self.assertEqual(response_data['status'], 'created')
    
    def test_get_payment_status_api(self):
        """Test payment status API"""
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='created'
        )
        
        response = self.client.get(
            f'/api/payment/status/{payment.id}',
            **self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(response_data['payment_id'], str(payment.id))
        self.assertEqual(response_data['status'], 'created')
    
    def test_get_payment_history_api(self):
        """Test payment history API"""
        # Create multiple payments
        for i in range(3):
            Payment.objects.create(
                user=self.user,
                amount=Decimal('29.00'),
                currency='USD',
                membership_tier='premium',
                status='created'
            )
        
        response = self.client.get(
            '/api/payment/history',
            **self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertEqual(len(response_data['payments']), 3)
        self.assertEqual(response_data['total'], 3)
    
    @patch('payment.api.DefaultAlipayClient')
    def test_create_antom_session_api(self, mock_client):
        """Test Antom session creation API"""
        # Mock Antom client response
        mock_instance = MagicMock()
        mock_instance.execute.return_value = json.dumps({
            'paymentSessionData': {
                'paymentSessionUrl': 'https://example.com/pay'
            }
        })
        mock_client.return_value = mock_instance
        
        # Create payment record first
        payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='created'
        )
        
        data = {'payment_id': str(payment.id)}
        
        response = self.client.post(
            '/api/payment/session',
            data=json.dumps(data),
            content_type='application/json',
            **self.get_auth_headers()
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = response.json()
        self.assertTrue(response_data['success'])
        self.assertEqual(response_data['payment_id'], str(payment.id))
        
        # Check payment status was updated
        payment.refresh_from_db()
        self.assertEqual(payment.status, 'pending')


class PaymentCallbackTest(TestCase):
    """Test payment callback handling"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(phone="13800138000")
        self.payment = Payment.objects.create(
            user=self.user,
            amount=Decimal('29.00'),
            currency='USD',
            membership_tier='premium',
            status='pending'
        )
    
    @patch('payment.api.check_signature')
    def test_successful_payment_callback(self, mock_check_signature):
        """Test successful payment callback"""
        mock_check_signature.return_value = True

        # Create the callback data in the format expected by Antom
        callback_data = {
            'notifyType': 'PAYMENT_RESULT',
            'paymentRequestId': str(self.payment.id),
            'result': {
                'resultCode': 'SUCCESS',
                'resultMessage': 'Payment successful'
            }
        }

        # Simulate the callback request with proper headers
        response = self.client.post(
            '/api/payment/receiveNotify',
            data=json.dumps(callback_data),
            content_type='application/json',
            **{
                'HTTP_REQUEST_METHOD': 'POST',
                'HTTP_REQUEST_URI': '/api/payment/receiveNotify',
                'HTTP_CLIENT_ID': 'test_client',
                'HTTP_REQUEST_TIME': '1234567890',
                'HTTP_SIGNATURE': 'test_signature'
            }
        )

        self.assertEqual(response.status_code, 200)

        # Check payment was updated
        self.payment.refresh_from_db()
        self.assertEqual(self.payment.status, 'success')
        self.assertIsNotNone(self.payment.paid_at)

        # Check subscription was updated
        subscription = self.user.subscription
        self.assertEqual(subscription.subscription_type, 'premium')
        self.assertTrue(subscription.is_active)
