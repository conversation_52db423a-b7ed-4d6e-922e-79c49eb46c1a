from __future__ import annotations
import json
import time
from ninja_extra import api_controller, route
from ninja.errors import HttpError
from ninja_jwt.authentication import <PERSON><PERSON><PERSON><PERSON>
from django.http import HttpRequest
from django.conf import settings
from django.db import transaction
from django.utils import timezone
from decimal import Decimal

from com.alipay.ams.api.default_alipay_client import DefaultAlipayClient
from com.alipay.ams.api.exception.exception import <PERSON><PERSON>y<PERSON>piException
from com.alipay.ams.api.model.amount import Amount
from com.alipay.ams.api.model.buyer import Buyer
from com.alipay.ams.api.model.goods import Goods
from com.alipay.ams.api.model.order import Order
from com.alipay.ams.api.model.product_code_type import ProductCodeType
from com.alipay.ams.api.request.notify.alipay_capture_result_notify import AlipayCaptureResultNotify
from com.alipay.ams.api.request.notify.alipay_pay_result_notify import AlipayPayResultNotify
from com.alipay.ams.api.request.pay.alipay_create_session_request import AlipayCreateSessionRequest
from com.alipay.ams.api.tools.webhook_tool import check_signature

from .models import Payment
from .schemas import (
    PaymentCreateRequest, PaymentCreateResponse, AntomSessionRequest, AntomSessionResponse,
    PaymentStatusResponse, PaymentHistoryResponse,
    SubscriptionUpdateRequest, SubscriptionUpdateResponse,
    NotifyResult, NotifyResponse
)
from .services import PaymentService


@api_controller("/payment", tags=["支付"])
class AntomPaymentController:
    def __init__(self):
        self.default_alipay_client = DefaultAlipayClient(
            settings.ANTOM_GATEWAY_URL,
            settings.ANTOM_CLIENT_ID,
            settings.ANTOM_MERCHANT_PRIVATE_KEY,
            settings.ANTOM_PUBLIC_KEY
        )
        # 记录当前使用的环境
        self.current_environment = getattr(settings, 'ANTOM_ENVIRONMENT', 'sandbox')
        print(f"Antom Payment Controller initialized with environment: {self.current_environment}")

    @route.get("/config", response=dict)
    def get_payment_config(self, request):
        """获取支付配置信息（不包含敏感信息）"""
        return {
            "environment": getattr(settings, 'ANTOM_ENVIRONMENT', 'sandbox'),
            "gateway_url": settings.ANTOM_GATEWAY_URL,
            "client_id_preview": settings.ANTOM_CLIENT_ID[:8] + '...' if settings.ANTOM_CLIENT_ID else 'Not Set',
            "notify_url": settings.ANTOM_NOTIFY_URL,
            "redirect_url": settings.ANTOM_REDIRECT_URL,
            "supported_currencies": ["HKD", "USD", "CNY", "EUR", "GBP"],
            "supported_tiers": ["free", "premium", "plus"]
        }

    @route.post("/create", response=PaymentCreateResponse, auth=JWTAuth())
    def create_payment_record(self, request, data: PaymentCreateRequest):
        """创建支付记录"""
        try:
            with transaction.atomic():
                # 创建支付记录
                payment = Payment.objects.create(
                    user=request.auth,
                    amount=Decimal(str(data.amount)),
                    currency=data.currency,
                    membership_tier=data.membership_tier,
                    status='created'
                )

                return PaymentCreateResponse(
                    payment_id=str(payment.id),
                    amount=float(payment.amount),
                    currency=payment.currency,
                    membership_tier=payment.membership_tier,
                    status=payment.status,
                    created_at=payment.created_at
                )
        except Exception as e:
            raise HttpError(500, f"Failed to create payment record: {str(e)}")

    @route.post("/session", response=AntomSessionResponse, auth=JWTAuth())
    def create_antom_session(self, request, data: AntomSessionRequest):
        """创建Antom支付会话"""
        try:
            # 获取支付记录
            payment = Payment.objects.get(id=data.payment_id, user=request.auth)

            if payment.status != 'created':
                raise HttpError(400, "Payment record is not in created status")

            # 创建Antom会话请求
            alipay_create_session_request = AlipayCreateSessionRequest()
            alipay_create_session_request.product_code = ProductCodeType.CASHIER_PAYMENT
            alipay_create_session_request.product_scene = "CHECKOUT_PAYMENT"

            # 设置金额
            amount = Amount(payment.currency, payment.amount_in_cents)
            alipay_create_session_request.payment_amount = amount

            # 使用本地支付记录ID作为支付请求ID
            alipay_create_session_request.payment_request_id = str(payment.id)

            # 设置买家信息
            buyer = Buyer()
            buyer.reference_buyer_id = str(request.auth.id)

            # 设置商品信息
            goods = Goods()
            goods.goods_brand = "Z-Aiden"
            goods.goods_category = "subscription"
            goods.goods_name = f"{payment.membership_tier.title()} Subscription"
            goods.goods_quantity = "1"
            goods.goods_sku_name = payment.membership_tier
            goods.goods_unit_amount = amount
            goods.reference_goods_id = payment.membership_tier

            # 设置订单信息
            order = Order()
            order.reference_order_id = str(payment.id)
            order.order_description = f"Z-Aiden {payment.membership_tier} subscription"
            order.order_amount = amount
            order.buyer = buyer
            order.goods = goods
            alipay_create_session_request.order = order

            # 设置回调和重定向URL
            alipay_create_session_request.payment_notify_url = settings.ANTOM_NOTIFY_URL
            alipay_create_session_request.payment_redirect_url = f"{settings.ANTOM_REDIRECT_URL}?paymentId={payment.id}"

            # 调用Antom API
            start_time = time.time()
            alipay_create_session_response_body = self.default_alipay_client.execute(alipay_create_session_request)
            print(f"Antom session creation took: {time.time() - start_time} seconds")

            # 解析响应
            session_data = json.loads(alipay_create_session_response_body)

            # 更新支付记录
            with transaction.atomic():
                payment.antom_payment_request_id = str(payment.id)
                payment.status = 'pending'
                payment.metadata.update({
                    'antom_session_data': session_data,
                    'session_created_at': timezone.now().isoformat()
                })
                payment.save()

            return AntomSessionResponse(
                success=True,
                payment_id=str(payment.id),
                antom_payment_request_id=str(payment.id),
                session_data=session_data,
                redirect_url=session_data.get('paymentSessionData', {}).get('paymentSessionUrl')
            )

        except Payment.DoesNotExist:
            raise HttpError(404, "Payment record not found")
        except AlipayApiException as e:
            raise HttpError(500, f"Antom session creation failed: {str(e)}")
        except Exception as e:
            raise HttpError(500, f"Failed to create Antom session: {str(e)}")

    @route.get("/status/{payment_id}", response=PaymentStatusResponse, auth=JWTAuth())
    def get_payment_status(self, request, payment_id: str):
        """获取支付状态"""
        try:
            payment = Payment.objects.get(id=payment_id, user=request.auth)

            return PaymentStatusResponse(
                payment_id=str(payment.id),
                status=payment.status,
                amount=float(payment.amount),
                currency=payment.currency,
                membership_tier=payment.membership_tier,
                created_at=payment.created_at,
                updated_at=payment.updated_at,
                paid_at=payment.paid_at,
                antom_payment_request_id=payment.antom_payment_request_id
            )
        except Payment.DoesNotExist:
            raise HttpError(404, "Payment record not found")

    @route.get("/history", response=PaymentHistoryResponse, auth=JWTAuth())
    def get_payment_history(self, request, page: int = 1, page_size: int = 10):
        """获取支付历史"""
        try:
            payments = Payment.objects.filter(user=request.auth).order_by('-created_at')
            total = payments.count()

            start = (page - 1) * page_size
            end = start + page_size
            page_payments = payments[start:end]

            payment_list = [
                PaymentStatusResponse(
                    payment_id=str(p.id),
                    status=p.status,
                    amount=float(p.amount),
                    currency=p.currency,
                    membership_tier=p.membership_tier,
                    created_at=p.created_at,
                    updated_at=p.updated_at,
                    paid_at=p.paid_at,
                    antom_payment_request_id=p.antom_payment_request_id
                ) for p in page_payments
            ]

            return PaymentHistoryResponse(
                payments=payment_list,
                total=total,
                page=page,
                page_size=page_size
            )
        except Exception as e:
            raise HttpError(500, f"Failed to get payment history: {str(e)}")

    # receive notify
    @route.post("/receiveNotify", response=NotifyResponse)
    def receive_notify(self, request: HttpRequest):
        # retrieve the required parameters from http request
        notify_body = request.body.decode('utf-8')
        request_uri = request.path
        request_method = request.method

        # retrieve the required parameters from request header
        request_time = request.headers.get("request-time")
        client_id = request.headers.get("client-id")
        signature = request.headers.get("signature")

        try:
            # verify the signature of notification
            verify_result = check_signature(request_method, request_uri, client_id, request_time, str(notify_body),
                                            signature,
                                            settings.ANTOM_PUBLIC_KEY)
            if not verify_result:
                raise Exception("Invalid notify signature")

            # deserialize the notification body
            notify = json.loads(notify_body)
            if notify['notifyType'] == "PAYMENT_RESULT":
                alipay_pay_result_notify = AlipayPayResultNotify(notify_body)

                # 获取支付请求ID（应该是我们的本地支付记录ID）
                payment_request_id = notify.get('paymentRequestId')

                if alipay_pay_result_notify.result.result_code == "SUCCESS":
                    # 处理支付成功
                    try:
                        with transaction.atomic():
                            # 查找支付记录
                            payment = Payment.objects.get(id=payment_request_id)

                            # 更新支付状态
                            payment.status = 'success'
                            payment.paid_at = timezone.now()
                            payment.metadata.update({
                                'antom_callback_data': notify,
                                'callback_received_at': timezone.now().isoformat()
                            })
                            payment.save()

                            # 更新用户订阅
                            PaymentService.update_user_subscription(payment)

                            print(f"Payment {payment_request_id} processed successfully")
                    except Payment.DoesNotExist:
                        print(f"Payment record {payment_request_id} not found")
                    except Exception as e:
                        print(f"Error processing payment {payment_request_id}: {str(e)}")

                    return NotifyResponse(
                        result=NotifyResult(
                            resultCode="SUCCESS",
                            resultMessage="success.",
                            resultStatus="S"
                        )
                    )
                else:
                    # 处理支付失败
                    try:
                        with transaction.atomic():
                            payment = Payment.objects.get(id=payment_request_id)
                            payment.status = 'failed'
                            payment.metadata.update({
                                'antom_callback_data': notify,
                                'callback_received_at': timezone.now().isoformat(),
                                'failure_reason': alipay_pay_result_notify.result.result_message
                            })
                            payment.save()
                            print(f"Payment {payment_request_id} failed: {alipay_pay_result_notify.result.result_message}")
                    except Payment.DoesNotExist:
                        print(f"Payment record {payment_request_id} not found")
                    except Exception as e:
                        print(f"Error updating failed payment {payment_request_id}: {str(e)}")

                    return NotifyResponse(
                        result=NotifyResult(
                            resultCode="SUCCESS",
                            resultMessage="payment failed but notification processed.",
                            resultStatus="S"
                        )
                    )
            if notify['notifyType'] == "CAPTURE_RESULT":
                alipay_capture_result_notify = AlipayCaptureResultNotify(notify_body)
                if alipay_capture_result_notify.result.result_code == "SUCCESS":
                    # handle your own business logic.
                    print(f"receive capture notify: {notify_body}")
                    return NotifyResponse(
                        result=NotifyResult(
                            resultCode="SUCCESS",
                            resultMessage="success.",
                            resultStatus="S"
                        )
                    )

        except Exception as e:
            print(str(e))
            return NotifyResponse(
                result=NotifyResult(
                    resultCode="FAIL",
                    resultMessage="fail.",
                    resultStatus="F"
                )
            )

        return NotifyResponse(
            result=NotifyResult(
                resultCode="SYSTEM_ERROR",
                resultMessage="system error.",
                resultStatus="F"
            )
        )

    @route.post("/update-subscription", response=SubscriptionUpdateResponse, auth=JWTAuth())
    def update_subscription_manual(self, request, data: SubscriptionUpdateRequest):
        """手动更新订阅状态（管理员功能）"""
        try:
            payment = Payment.objects.get(id=data.payment_id)

            if not payment.is_successful:
                raise HttpError(400, "Payment is not successful")

            with transaction.atomic():
                PaymentService.update_user_subscription(payment)

                # 获取更新后的订阅信息
                subscription = payment.user.subscription

                return SubscriptionUpdateResponse(
                    success=True,
                    user_id=payment.user.id,
                    subscription_type=subscription.subscription_type,
                    start_date=subscription.start_date,
                    end_date=subscription.end_date,
                    is_active=subscription.is_active
                )

        except Payment.DoesNotExist:
            raise HttpError(404, "Payment record not found")
        except Exception as e:
            raise HttpError(500, f"Failed to update subscription: {str(e)}")

    @route.get("/admin/payments", response=PaymentHistoryResponse)
    def get_all_payments(self, request, page: int = 1, page_size: int = 20, status: str = None):
        """获取所有支付记录（管理员功能）"""
        # Note: In production, add proper admin authentication
        try:
            payments_query = Payment.objects.all().order_by('-created_at')

            if status:
                payments_query = payments_query.filter(status=status)

            total = payments_query.count()

            start = (page - 1) * page_size
            end = start + page_size
            page_payments = payments_query[start:end]

            payment_list = [
                PaymentStatusResponse(
                    payment_id=str(p.id),
                    status=p.status,
                    amount=float(p.amount),
                    currency=p.currency,
                    membership_tier=p.membership_tier,
                    created_at=p.created_at,
                    updated_at=p.updated_at,
                    paid_at=p.paid_at,
                    antom_payment_request_id=p.antom_payment_request_id
                ) for p in page_payments
            ]

            return PaymentHistoryResponse(
                payments=payment_list,
                total=total,
                page=page,
                page_size=page_size
            )
        except Exception as e:
            raise HttpError(500, f"Failed to get payments: {str(e)}")

    @route.get("/admin/payment/{payment_id}", response=PaymentStatusResponse)
    def get_payment_details(self, request, payment_id: str):
        """获取支付详情（管理员功能）"""
        try:
            payment = Payment.objects.get(id=payment_id)

            return PaymentStatusResponse(
                payment_id=str(payment.id),
                status=payment.status,
                amount=float(payment.amount),
                currency=payment.currency,
                membership_tier=payment.membership_tier,
                created_at=payment.created_at,
                updated_at=payment.updated_at,
                paid_at=payment.paid_at,
                antom_payment_request_id=payment.antom_payment_request_id
            )
        except Payment.DoesNotExist:
            raise HttpError(404, "Payment record not found")

    @route.post("/admin/payment/{payment_id}/status")
    def update_payment_status_admin(self, request, payment_id: str, status: str):
        """更新支付状态（管理员功能）"""
        try:
            payment = PaymentService.update_payment_status(
                payment_id=payment_id,
                status=status,
                metadata={'admin_updated': True, 'admin_updated_at': timezone.now().isoformat()}
            )

            return {"success": True, "message": f"Payment status updated to {status}"}
        except Payment.DoesNotExist:
            raise HttpError(404, "Payment record not found")
        except ValueError as e:
            raise HttpError(400, str(e))
        except Exception as e:
            raise HttpError(500, f"Failed to update payment status: {str(e)}")

