"""
Django management command to check Antom configuration
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils.termcolors import make_style


class Command(BaseCommand):
    help = 'Check Antom payment configuration and environment settings'

    def add_arguments(self, parser):
        parser.add_argument(
            '--show-keys',
            action='store_true',
            help='Show partial key information (for debugging)',
        )

    def handle(self, *args, **options):
        # Define styles for colored output
        success_style = make_style(opts=('bold',), fg='green')
        warning_style = make_style(opts=('bold',), fg='yellow')
        error_style = make_style(opts=('bold',), fg='red')
        info_style = make_style(opts=('bold',), fg='blue')

        self.stdout.write(info_style('=== Antom Payment Configuration Check ===\n'))

        # Current environment
        current_env = getattr(settings, 'ANTOM_ENVIRONMENT', 'Not Set')
        self.stdout.write(f"Current Environment: {success_style(current_env)}")

        # Environment info
        env_info = getattr(settings, 'ANTOM_CURRENT_ENVIRONMENT', {})
        if env_info:
            self.stdout.write(f"Gateway URL: {env_info.get('gateway_url', 'Not Set')}")
            self.stdout.write(f"Client ID: {env_info.get('client_id', 'Not Set')}")

        self.stdout.write("\n" + info_style("=== Configuration Status ==="))

        # Check required settings
        required_settings = [
            'ANTOM_CLIENT_ID',
            'ANTOM_PUBLIC_KEY',
            'ANTOM_MERCHANT_PRIVATE_KEY',
            'ANTOM_GATEWAY_URL',
            'ANTOM_NOTIFY_URL',
            'ANTOM_REDIRECT_URL'
        ]

        all_configured = True
        for setting_name in required_settings:
            value = getattr(settings, setting_name, None)
            if value and value not in ['your_client_id', 'antom_public_key', 'your_private_key', 
                                      'sandbox_client_id', 'sandbox_public_key', 'sandbox_private_key',
                                      'production_client_id', 'production_public_key', 'production_private_key']:
                status = success_style('✓ Configured')
                if options['show_keys'] and 'KEY' in setting_name:
                    # Show first and last few characters for debugging
                    if len(value) > 20:
                        display_value = f"{value[:10]}...{value[-10:]}"
                    else:
                        display_value = f"{value[:5]}...{value[-5:]}"
                    status += f" ({display_value})"
            else:
                status = error_style('✗ Not Configured')
                all_configured = False
            
            self.stdout.write(f"{setting_name}: {status}")

        # Environment-specific configuration check
        self.stdout.write("\n" + info_style("=== Environment-Specific Configuration ==="))
        
        if current_env == 'sandbox':
            env_settings = [
                ('ANTOM_SANDBOX_CLIENT_ID', getattr(settings, 'ANTOM_SANDBOX_CLIENT_ID', None)),
                ('ANTOM_SANDBOX_PUBLIC_KEY', getattr(settings, 'ANTOM_SANDBOX_PUBLIC_KEY', None)),
                ('ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY', getattr(settings, 'ANTOM_SANDBOX_MERCHANT_PRIVATE_KEY', None)),
            ]
        else:
            env_settings = [
                ('ANTOM_PRODUCTION_CLIENT_ID', getattr(settings, 'ANTOM_PRODUCTION_CLIENT_ID', None)),
                ('ANTOM_PRODUCTION_PUBLIC_KEY', getattr(settings, 'ANTOM_PRODUCTION_PUBLIC_KEY', None)),
                ('ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY', getattr(settings, 'ANTOM_PRODUCTION_MERCHANT_PRIVATE_KEY', None)),
            ]

        for setting_name, value in env_settings:
            if value and 'client_id' not in value and 'public_key' not in value and 'private_key' not in value:
                status = success_style('✓ Configured')
            else:
                status = warning_style('⚠ Using Default/Placeholder')
            self.stdout.write(f"{setting_name}: {status}")

        # Summary
        self.stdout.write("\n" + info_style("=== Summary ==="))
        if all_configured:
            self.stdout.write(success_style("✓ All required settings are configured"))
        else:
            self.stdout.write(error_style("✗ Some settings need configuration"))
            self.stdout.write(warning_style("Please check your secrets.env file"))

        # Environment switch instructions
        self.stdout.write("\n" + info_style("=== Environment Switching ==="))
        self.stdout.write("To switch environments, set ANTOM_ENVIRONMENT in your secrets.env file:")
        self.stdout.write("  • ANTOM_ENVIRONMENT=sandbox  (for testing)")
        self.stdout.write("  • ANTOM_ENVIRONMENT=production  (for live payments)")

        # URLs check
        self.stdout.write("\n" + info_style("=== URL Configuration ==="))
        notify_url = getattr(settings, 'ANTOM_NOTIFY_URL', '')
        redirect_url = getattr(settings, 'ANTOM_REDIRECT_URL', '')
        
        if 'localhost' in notify_url and current_env == 'production':
            self.stdout.write(warning_style("⚠ Warning: Using localhost in production notify URL"))
        
        if 'localhost' in redirect_url and current_env == 'production':
            self.stdout.write(warning_style("⚠ Warning: Using localhost in production redirect URL"))

        self.stdout.write(f"Notify URL: {notify_url}")
        self.stdout.write(f"Redirect URL: {redirect_url}")

        self.stdout.write("\n" + info_style("=== Next Steps ==="))
        if not all_configured:
            self.stdout.write("1. Copy secrets.env.example to secrets.env")
            self.stdout.write("2. Fill in your Antom credentials")
            self.stdout.write("3. Set ANTOM_ENVIRONMENT to 'sandbox' or 'production'")
            self.stdout.write("4. Run this command again to verify configuration")
        else:
            self.stdout.write("Configuration looks good! You can now test payments.")
            if current_env == 'sandbox':
                self.stdout.write("Remember to switch to 'production' for live payments.")
